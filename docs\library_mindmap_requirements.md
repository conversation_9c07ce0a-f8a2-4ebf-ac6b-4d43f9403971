# 📚 Python库/框架学习思维导图需求文档

## 🎯 核心需求

为指定的库/框架创建**详细的HTML交互式思维导图**，用于系统性学习和API参考。

### 目标库/框架
- **库名称**: `{LIBRARY_NAME}`
- **版本要求**: 最新稳定版本

---

## 🛠️ 工具使用要求

### 必须使用的工具组合
1. **deepwiki工具**
   - 获取`{LIBRARY_NAME}`的完整文档结构
   - 提取概念说明和架构信息
   - 了解库的设计理念和核心特性

2. **context7工具**
   - 获取`{LIBRARY_NAME}`的最新代码示例
   - 提取实际API使用方法
   - 收集真实的编程模式和最佳实践

3. **sequentialthinking工具**
   - 分析和整理收集到的信息
   - 构建逻辑清晰的思维导图结构
   - 确保内容的完整性和准确性

### 工具使用流程
```
deepwiki → context7 → sequentialthinking → HTML生成 → 浏览器验证
```

---

## 📋 输出格式要求

### 文件格式
- **主文件**: HTML文件
  - **文件名**: `{library_name}_complete_mindmap.html`
  - **位置**: `docs/` 目录下
  - **内容**: 列表视图 + 图形视图入口
- **图形视图文件**: 独立HTML文件
  - **文件名**: `{library_name}_graph_view.html`
  - **位置**: `docs/` 目录下
  - **内容**: 全屏可视化思维导图

### 交互式功能

#### 基础功能
- ✅ **搜索功能**: 支持API方法、类名、功能关键词搜索
- ✅ **折叠/展开**: 支持节点的展开全部、收起全部操作
- ✅ **代码示例**: 点击节点显示具体使用代码
- ✅ **分层着色**: 不同层级使用不同颜色区分
- ✅ **响应式设计**: 适配桌面和移动设备

#### 双视图模式
- ✅ **📋 列表视图**: 传统的文本式思维导图展示
  - 层级缩进显示
  - 文本搜索高亮
  - 代码示例折叠/展开
  - 节点类型标签
- ✅ **🌐 图形视图**: 独立页面的交互式可视化思维导图
  - 独立HTML页面，不受框架限制
  - D3.js驱动的动态图形
  - 节点点击展开/收起
  - 缩放和拖拽操作
  - 智能搜索高亮
  - 全屏显示，更好的视觉体验

#### 可视化图形功能
- ✅ **节点设计**: 方框节点，自适应内容大小
- ✅ **颜色编码**: 根据节点类型使用不同颜色
- ✅ **交互控制**:
  - 🔍 重置缩放
  - 📐 适应屏幕
  - ➕ 展开全部节点
  - ➖ 收起全部节点
  - 📤 导出Mermaid
- ✅ **信息面板**: 点击节点显示详细信息和代码示例
- ✅ **图例说明**: 清晰的颜色含义说明
- ✅ **平滑动画**: 节点展开/收起的流畅过渡效果
- ✅ **导出功能**: 支持导出为Mermaid格式
  - 📄 生成标准Mermaid语法
  - 💾 一键下载.mmd文件
  - 🔗 保持节点层级关系
  - 🎨 包含颜色和样式信息

### 视觉设计

#### 整体设计
- **主题色彩**: 渐变背景，现代化UI设计
- **响应式布局**: 适配不同屏幕尺寸
- **统计信息**: 显示模块数量、API数量、代码示例数量

#### 列表视图设计
- **层级区分**: 6个层级，每层不同颜色
- **标签系统**: method、property、class、event等API类型标签
- **悬停效果**: 鼠标悬停时的视觉反馈

#### 图形视图设计
- **节点样式**:
  - 🔲 方框节点，圆角设计
  - 📏 自适应文本内容大小
  - 🎨 基于类型的颜色编码
  - ⚪ 白色文字确保对比度
- **连接线**:
  - 🔗 贝塞尔曲线连接
  - 🎯 悬停高亮效果
- **颜色方案**:
  - 🔴 红色: 根节点
  - 🔵 蓝色: 分类节点
  - 🟢 绿色: 模块节点
  - 🟠 橙色: 类节点
  - 🟣 紫色: 方法/属性节点

---

## 📖 内容结构要求

### 思维导图层级结构
```
{LIBRARY_NAME} (根节点)
├── 核心架构
│   ├── 基础组件
│   ├── 核心类
│   └── 配置系统
├── 主要功能模块
│   ├── 功能分类1
│   ├── 功能分类2
│   └── 功能分类3
├── API参考
│   ├── 类方法
│   ├── 实例方法
│   ├── 属性
│   └── 事件
├── 高级特性
│   ├── 扩展功能
│   ├── 插件系统
│   └── 自定义配置
├── 工具和辅助
│   ├── 辅助类
│   ├── 工具函数
│   └── 常量定义
├── 错误处理
│   ├── 异常类型
│   ├── 错误处理策略
│   └── 调试方法
└── 最佳实践
    ├── 使用模式
    ├── 性能优化
    └── 常见问题
```

### 节点内容要求
每个节点必须包含：
- **标题**: 清晰的API名称或功能名称
- **描述**: 简洁的功能说明
- **类型标签**: method/property/class/event等
- **代码示例**: 实际可运行的代码片段
- **参数说明**: 方法参数和返回值（如适用）

---

## 🔍 详细程度要求

### 完整性标准
- ✅ **API覆盖率**: 覆盖所有主要的类、方法、属性
- ✅ **代码示例**: 每个重要API都要有使用示例
- ✅ **功能分类**: 按功能逻辑分组，不遗漏重要特性
- ✅ **层次清晰**: 从概念到具体实现的完整路径

### 质量标准
- **准确性**: 基于最新文档和代码示例
- **实用性**: 代码示例可直接使用
- **可读性**: 描述简洁明了，易于理解
- **完整性**: 不遗漏重要的API和功能

---

## 📊 统计信息要求

在思维导图头部显示：
- 📚 核心模块数量
- 🔧 API方法总数
- 💡 代码示例数量
- 🎯 功能覆盖范围

---

## 🎨 可视化思维导图技术规范

### 技术栈要求
- **D3.js v7**: 用于SVG图形渲染和数据绑定
- **现代CSS3**: 响应式布局、动画效果、渐变背景
- **原生JavaScript**: 交互逻辑、事件处理、数据管理
- **HTML5**: 语义化结构、无障碍访问支持

### 节点设计规范
```css
/* 节点样式要求 */
.node-rect {
    stroke: #fff;
    stroke-width: 2px;
    cursor: pointer;
    transition: all 0.3s ease;
    rx: 8;  /* 圆角半径 */
    ry: 8;
}

.node-text {
    font-family: 'Segoe UI', sans-serif;
    font-size: 12px;
    text-anchor: middle;
    fill: #fff;  /* 白色文字 */
    font-weight: 600;
    dominant-baseline: central;
}
```

### 交互行为规范
1. **节点点击**: 展开/收起子节点，显示详细信息
2. **悬停效果**: 边框加粗，亮度增加
3. **搜索高亮**: 匹配节点红色边框突出显示
4. **缩放拖拽**: 支持鼠标滚轮缩放，拖拽移动画布
5. **平滑动画**: 750ms过渡动画，贝塞尔曲线缓动

### 布局算法
- **树状布局**: 使用D3.js的tree()布局算法
- **节点间距**: 垂直180px，水平根据内容自适应
- **自适应尺寸**: 节点大小根据文本内容动态计算
- **最小尺寸**: 宽度60px，高度24px，内边距12px

### 颜色系统
```javascript
const nodeColors = {
    'root': '#e74c3c',      // 红色 - 根节点
    'category': '#3498db',   // 蓝色 - 分类
    'module': '#2ecc71',     // 绿色 - 模块
    'class': '#f39c12',      // 橙色 - 类
    'group': '#9b59b6',      // 紫色 - 分组
    'method': '#9b59b6',     // 紫色 - 方法
    'property': '#9b59b6',   // 紫色 - 属性
    'syntax': '#34495e',     // 深灰 - 语法
    'feature': '#16a085'     // 青色 - 特性
};
```

### Mermaid导出功能规范

#### 导出格式要求
```mermaid
graph TD
    A[根节点] --> B[分类1]
    A --> C[分类2]
    B --> D[模块1]
    B --> E[模块2]
    C --> F[API1]
    C --> G[API2]

    classDef rootNode fill:#e74c3c,stroke:#fff,stroke-width:2px,color:#fff
    classDef categoryNode fill:#3498db,stroke:#fff,stroke-width:2px,color:#fff
    classDef moduleNode fill:#2ecc71,stroke:#fff,stroke-width:2px,color:#fff
    classDef apiNode fill:#9b59b6,stroke:#fff,stroke-width:2px,color:#fff

    class A rootNode
    class B,C categoryNode
    class D,E moduleNode
    class F,G apiNode
```

#### 导出功能实现
- **语法生成**: 自动转换D3.js树结构为Mermaid graph TD语法
- **样式保持**: 保留原有的颜色编码和节点分类
- **文件下载**: 生成.mmd文件并触发浏览器下载
- **兼容性**: 确保导出的文件可在Mermaid Live Editor中正常显示

---

## 🚀 工作流程模板

### 第一步：信息收集
```bash
# 使用deepwiki获取文档结构
read_wiki_structure_deepwiki({LIBRARY_REPO})
read_wiki_contents_deepwiki({LIBRARY_REPO})

# 使用context7获取代码示例
resolve-library-id_Context_7({LIBRARY_NAME})
get-library-docs_Context_7({LIBRARY_ID})
```

### 第二步：数据分析
```bash
# 使用sequentialthinking分析整理
sequentialthinking_Sequential_thinking(
    分析收集到的信息
    构建思维导图结构
    确保内容完整性
)
```

### 第三步：HTML生成
```bash
# 创建主思维导图文件（列表视图 + 图形视图入口）
save-file(docs/{library_name}_complete_mindmap.html)

# 创建独立的图形视图文件（全屏可视化）
save-file(docs/{library_name}_graph_view.html)

# 必须包含的技术栈:
# - D3.js v7: 用于可视化图形渲染
# - 现代CSS3: 响应式设计和动画效果
# - 原生JavaScript: 交互逻辑和数据处理
# - Mermaid导出: 支持导出为.mmd格式
```
# - Mermaid导出: 支持导出为.mmd格式
```

### 第四步：验证
```bash
# 在浏览器中打开主文件验证
open-browser(file:///path/to/{library_name}_complete_mindmap.html)

# 验证独立图形视图
open-browser(file:///path/to/{library_name}_graph_view.html)
```

---

## ✅ 验收标准

### 功能验收

#### 基础功能验收
- [ ] 搜索功能正常工作（列表和图形视图）
- [ ] 折叠/展开功能正常
- [ ] 代码示例可以正确显示
- [ ] 响应式设计在不同设备上正常

#### 视图切换验收
- [ ] 列表视图和图形视图可以正常切换
- [ ] 两种视图的搜索功能都正常工作
- [ ] 视图切换时状态保持正确

#### 可视化图形验收
- [ ] D3.js图形正确渲染
- [ ] 节点点击展开/收起功能正常
- [ ] 缩放和拖拽操作流畅
- [ ] 信息面板正确显示节点详情
- [ ] 控制按钮功能正常（重置缩放、适应屏幕等）
- [ ] 搜索高亮在图形视图中正常工作
- [ ] 节点样式（方框、颜色、文字）正确显示
- [ ] 独立图形视图页面正常工作
- [ ] 全屏显示效果良好，无布局限制

#### Mermaid导出验收
- [ ] 导出按钮正常显示和工作
- [ ] 生成的Mermaid语法正确
- [ ] 保持原有的层级结构
- [ ] 文件下载功能正常
- [ ] 导出的.mmd文件可以在Mermaid编辑器中正常显示

### 内容验收
- [ ] 覆盖了`{LIBRARY_NAME}`的所有主要功能
- [ ] 每个API都有清晰的描述
- [ ] 代码示例准确可用
- [ ] 结构层次清晰合理

### 质量验收
- [ ] 信息来源权威（deepwiki + context7）
- [ ] 内容组织逻辑清晰
- [ ] 视觉设计美观易用
- [ ] 加载速度快，交互流畅

---

## 📝 使用说明

### 如何发起请求
只需提供：
1. **库名称**: 如 "FastAPI"、"Pandas"、"Selenium" 等
2. **特殊关注点**: 如果有特定的功能模块需要重点关注

### 示例请求
```
请为 FastAPI 创建详细的学习思维导图，重点关注路由和依赖注入功能。
```

### 预期交付
- 主HTML思维导图文件（列表视图 + 图形视图入口）
- 独立的图形视图HTML文件（全屏可视化）
- Mermaid导出功能
- 在浏览器中的演示
- 简要的使用说明

### 实际案例
**DrissionPage思维导图**
- 主文件: `docs/drissionpage_complete_mindmap.html`
- 图形视图: `docs/drissionpage_graph_view.html`
- ✅ 双视图模式：列表视图 + 独立图形视图
- ✅ 8大核心模块，200+ API方法，386个代码示例
- ✅ 完整的可视化交互功能
- ✅ 方框节点设计，颜色编码系统
- ✅ 搜索高亮，信息面板，控制按钮
- ✅ Mermaid导出功能
- ✅ 响应式设计，移动端适配
- ✅ 全屏图形视图，无布局限制

---

## 🔄 迭代改进

### 内容改进
- 可以要求增加特定模块的详细信息
- 可以要求添加更多代码示例
- 可以要求重新组织结构层次
- 可以要求补充特定功能的详细说明

### 视觉改进
- 可以要求调整视觉样式
- 可以要求修改节点形状（圆形、方形、其他形状）
- 可以要求调整颜色方案
- 可以要求优化布局和间距

### 功能改进
- 可以要求添加新的交互功能
- 可以要求优化搜索算法
- 可以要求增强可视化效果
- 可以要求添加更多导出功能（PDF、图片、JSON等）
- 可以要求改进Mermaid导出格式和样式

### 技术改进
- 可以要求性能优化
- 可以要求兼容性改进
- 可以要求添加新的可视化库支持
- 可以要求移动端体验优化

---

## 📈 版本历史

### v2.1 (2025-01-30)
- ✅ 图形视图独立页面化，不受框架限制
- ✅ 新增Mermaid导出功能
- ✅ 全屏可视化体验优化
- ✅ 双文件结构：主文件 + 独立图形视图

### v2.0 (2025-01-30)
- ✅ 新增可视化思维导图功能
- ✅ 双视图模式（列表视图 + 图形视图）
- ✅ D3.js驱动的交互式图形
- ✅ 方框节点设计，自适应内容大小
- ✅ 完整的交互控制和信息面板
- ✅ 搜索高亮在两种视图中都支持

### v1.0 (2025-01-29)
- ✅ 基础HTML思维导图功能
- ✅ 文本式层级展示
- ✅ 搜索和折叠功能
- ✅ 代码示例展示
- ✅ 响应式设计

---

*最后更新: 2025-01-30*
*适用于: 库/框架学习思维导图创建*
*当前版本: v2.1 - 独立图形视图 + Mermaid导出*
