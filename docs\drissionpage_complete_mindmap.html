<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DrissionPage 完整学习思维导图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
            margin-bottom: 20px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-item {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }

        .view-selector {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .view-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .view-btn.active {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }

        .view-btn:not(.active) {
            background: rgba(255, 255, 255, 0.9);
            color: #2c3e50;
        }

        .view-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .search-box {
            flex: 1;
            min-width: 300px;
            padding: 12px 20px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 1em;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-box:focus {
            border-color: #3498db;
        }

        .control-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            background: #3498db;
            color: white;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .mindmap-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            min-height: 600px;
        }

        .mindmap-tree {
            font-family: 'Consolas', 'Monaco', monospace;
            line-height: 1.6;
        }

        .node {
            margin: 8px 0;
            padding: 8px 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .node:hover {
            transform: translateX(5px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .node-level-0 { background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; font-weight: bold; font-size: 1.3em; }
        .node-level-1 { background: linear-gradient(135deg, #3498db, #2980b9); color: white; font-weight: 600; margin-left: 20px; }
        .node-level-2 { background: linear-gradient(135deg, #2ecc71, #27ae60); color: white; font-weight: 500; margin-left: 40px; }
        .node-level-3 { background: linear-gradient(135deg, #f39c12, #e67e22); color: white; margin-left: 60px; }
        .node-level-4 { background: linear-gradient(135deg, #9b59b6, #8e44ad); color: white; margin-left: 80px; }
        .node-level-5 { background: linear-gradient(135deg, #34495e, #2c3e50); color: white; margin-left: 100px; }

        .node-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .node-icon {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .node-title {
            font-weight: 600;
        }

        .node-tag {
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .node-description {
            margin-top: 5px;
            font-size: 0.9em;
            opacity: 0.9;
        }

        .code-example {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            display: none;
        }

        .code-example.show {
            display: block;
        }

        .expand-icon {
            transition: transform 0.3s ease;
            margin-right: 5px;
        }

        .expand-icon.expanded {
            transform: rotate(90deg);
        }

        .hidden {
            display: none !important;
        }

        .highlight {
            background: #fff3cd !important;
            border: 2px solid #ffc107 !important;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .controls {
                flex-direction: column;
            }
            
            .search-box {
                min-width: 100%;
            }
            
            .view-selector {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐍 DrissionPage 完整学习思维导图</h1>
            <p>Python 强大优雅的网页自动化工具 - 浏览器控制与数据包处理的完美结合</p>
            
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number">6</span>
                    <span>主要分类</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">25+</span>
                    <span>功能模块</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">60+</span>
                    <span>API方法</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">50+</span>
                    <span>代码示例</span>
                </div>
            </div>
        </div>

        <div class="view-selector">
            <button class="view-btn active" onclick="showListView()">📋 列表视图</button>
            <a href="drissionpage_graph_view.html" class="view-btn">🌐 图形视图</a>
        </div>

        <div class="controls">
            <input type="text" class="search-box" placeholder="🔍 搜索API方法、类名、功能关键词..." oninput="searchNodes(this.value)">
            <button class="control-btn" onclick="expandAll()">➕ 展开全部</button>
            <button class="control-btn" onclick="collapseAll()">➖ 收起全部</button>
            <button class="control-btn" onclick="showAllExamples()">💡 显示所有示例</button>
            <button class="control-btn" onclick="hideAllExamples()">📦 隐藏所有示例</button>
        </div>

        <div class="mindmap-container">
            <div class="mindmap-tree" id="mindmap-tree">
                <!-- 思维导图内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 思维导图数据结构
        const mindmapData = {
            title: "DrissionPage",
            type: "root",
            description: "Python 强大优雅的网页自动化工具",
            children: [
                {
                    title: "核心架构",
                    type: "category",
                    description: "DrissionPage的基础架构和核心组件",
                    children: [
                        {
                            title: "基础组件",
                            type: "module",
                            description: "核心基础类和接口",
                            children: [
                                {
                                    title: "BasePage",
                                    type: "class",
                                    description: "所有页面对象的基类",
                                    code: `# BasePage 提供基础的页面操作接口
class BasePage:
    def ele(self, locator):
        """查找单个元素"""
        pass

    def eles(self, locator):
        """查找多个元素"""
        pass`
                                },
                                {
                                    title: "Driver",
                                    type: "class",
                                    description: "Chrome DevTools Protocol 驱动器",
                                    code: `# Driver 负责与浏览器的底层通信
from DrissionPage._base.driver import Driver

driver = Driver()
# 执行CDP命令
result = driver.run('Page.navigate', url='https://example.com')`
                                },
                                {
                                    title: "Chromium",
                                    type: "class",
                                    description: "浏览器管理器，负责启动和管理浏览器进程",
                                    code: `# Chromium 管理浏览器实例
from DrissionPage._base.chromium import Chromium

chromium = Chromium()
# 启动浏览器
chromium.start()
# 连接到浏览器
chromium.connect()`
                                }
                            ]
                        },
                        {
                            title: "页面对象",
                            type: "module",
                            description: "三种主要的页面操作对象",
                            children: [
                                {
                                    title: "ChromiumPage",
                                    type: "class",
                                    description: "浏览器模式页面对象，提供完整的浏览器控制功能",
                                    code: `# ChromiumPage - 浏览器控制模式
from DrissionPage import ChromiumPage

# 创建页面对象
page = ChromiumPage()
# 访问网页
page.get('https://www.baidu.com')
# 查找元素并操作
page.ele('#kw').input('DrissionPage')
page.ele('#su').click()`
                                },
                                {
                                    title: "SessionPage",
                                    type: "class",
                                    description: "会话模式页面对象，基于requests库的HTTP请求",
                                    code: `# SessionPage - 会话请求模式
from DrissionPage import SessionPage

# 创建页面对象
page = SessionPage()
# 发送GET请求
page.get('https://gitee.com/explore/all')
# 查找元素
items = page.eles('t:h3')
for item in items:
    print(item.text)`
                                },
                                {
                                    title: "WebPage",
                                    type: "class",
                                    description: "混合模式页面对象，可在浏览器和会话模式间切换",
                                    code: `# WebPage - 混合模式
from DrissionPage import WebPage

# 创建页面对象（默认浏览器模式）
page = WebPage()
# 浏览器模式登录
page.get('https://gitee.com/login')
page.ele('#user_login').input('username')
page.ele('#user_password').input('password')
page.ele('@value=登录').click()

# 切换到会话模式进行数据采集
page.change_mode('s')
page.get('https://gitee.com/explore')
data = page.eles('.item')`
                                }
                            ]
                        },
                        {
                            title: "配置系统",
                            type: "module",
                            description: "浏览器和会话的配置管理",
                            children: [
                                {
                                    title: "ChromiumOptions",
                                    type: "class",
                                    description: "浏览器配置选项",
                                    code: `# ChromiumOptions - 浏览器配置
from DrissionPage import ChromiumOptions, ChromiumPage

# 创建配置对象
co = ChromiumOptions()
# 设置浏览器路径
co.set_browser_path(r'D:\\chrome.exe')
# 设置用户数据目录
co.set_user_data_path(r'D:\\user_data')
# 无头模式
co.headless()
# 隐身模式
co.incognito()
# 禁用图片加载
co.no_imgs(True)

# 使用配置创建页面
page = ChromiumPage(co)`
                                },
                                {
                                    title: "SessionOptions",
                                    type: "class",
                                    description: "会话配置选项",
                                    code: `# SessionOptions - 会话配置
from DrissionPage import SessionOptions, SessionPage

# 创建配置对象
so = SessionOptions()
# 设置请求头
so.set_headers({
    'User-Agent': 'Custom Agent',
    'Accept-Language': 'zh-CN,zh;q=0.9'
})
# 设置代理
so.set_proxies('http://localhost:1080')
# 设置超时
so.set_timeout(30)

# 使用配置创建页面
page = SessionPage(so)`
                                }
                            ]
                        }
                    ]
                },
                {
                    title: "主要功能模块",
                    type: "category",
                    description: "DrissionPage的核心功能模块",
                    children: [
                        {
                            title: "浏览器控制模式",
                            type: "module",
                            description: "完整的浏览器自动化功能",
                            children: [
                                {
                                    title: "页面导航",
                                    type: "feature",
                                    description: "页面访问和导航控制",
                                    code: `# 页面导航操作
page = ChromiumPage()

# 访问URL
page.get('https://www.baidu.com')
# 刷新页面
page.refresh()
# 后退
page.back()
# 前进
page.forward()
# 停止加载
page.stop_loading()

# 设置加载模式
page.set.load_mode.none()  # 不等待加载完成
page.set.load_mode.normal()  # 等待页面加载
page.set.load_mode.eager()  # 等待DOM加载`
                                },
                                {
                                    title: "标签管理",
                                    type: "feature",
                                    description: "浏览器标签页的创建和管理",
                                    code: `# 标签页管理
page = ChromiumPage()

# 创建新标签
tab = page.new_tab()
tab_with_url = page.new_tab('https://www.baidu.com')

# 获取标签对象
current_tab = page.get_tab()
tab_by_index = page.get_tab(1)
tab_by_id = page.get_tab('tab_id')

# 切换标签
page.to_tab(tab)
page.to_tab(1)

# 关闭标签
tab.close()
page.close_tabs([tab1, tab2])`
                                }
                            ]
                        },
                        {
                            title: "会话请求模式",
                            type: "module",
                            description: "基于requests的HTTP请求功能",
                            children: [
                                {
                                    title: "HTTP请求",
                                    type: "feature",
                                    description: "各种HTTP请求方法",
                                    code: `# HTTP请求操作
page = SessionPage()

# GET请求
response = page.get('https://httpbin.org/get')
# POST请求
response = page.post('https://httpbin.org/post',
                    data={'key': 'value'})
# PUT请求
response = page.put('https://httpbin.org/put',
                   json={'data': 'test'})
# DELETE请求
response = page.delete('https://httpbin.org/delete')

# 获取响应内容
print(response.text)
print(response.json())
print(response.status_code)`
                                },
                                {
                                    title: "数据提取",
                                    type: "feature",
                                    description: "从HTML响应中提取数据",
                                    code: `# 数据提取示例
page = SessionPage()
page.get('https://news.ycombinator.com')

# 提取标题
titles = page.eles('.titleline>a')
for title in titles:
    print(title.text)
    print(title.link)

# 提取表格数据
table = page.ele('table')
rows = table.eles('tr')
for row in rows:
    cells = row.eles('td')
    data = [cell.text for cell in cells]
    print(data)`
                                }
                            ]
                        },
                        {
                            title: "混合模式",
                            type: "module",
                            description: "浏览器和会话模式的无缝切换",
                            children: [
                                {
                                    title: "模式切换",
                                    type: "feature",
                                    description: "在不同模式间切换",
                                    code: `# 模式切换示例
page = WebPage()

# 默认浏览器模式
print(f"当前模式: {page.mode}")

# 浏览器模式登录
page.get('https://example.com/login')
page.ele('#username').input('user')
page.ele('#password').input('pass')
page.ele('#login').click()

# 切换到会话模式
page.change_mode('s')
print(f"切换后模式: {page.mode}")

# 会话模式下访问需要登录的页面
page.get('https://example.com/protected')
data = page.eles('.data-item')

# 切换回浏览器模式
page.change_mode('d')`
                                },
                                {
                                    title: "状态保持",
                                    type: "feature",
                                    description: "在模式切换时保持登录状态",
                                    code: `# 状态保持示例
page = WebPage()

# 浏览器模式登录
page.get('https://github.com/login')
page.ele('#login_field').input('username')
page.ele('#password').input('password')
page.ele('@name=commit').click()

# 等待登录完成
page.wait.load_start()

# 切换到会话模式，自动同步cookies
page.change_mode('s')

# 会话模式下访问需要登录的API
api_response = page.get('https://api.github.com/user')
user_info = api_response.json()
print(user_info)`
                                }
                            ]
                        }
                    ]
                },
                {
                    title: "API参考",
                    type: "category",
                    description: "完整的API方法参考",
                    children: [
                        {
                            title: "页面操作方法",
                            type: "module",
                            description: "页面级别的操作方法",
                            children: [
                                {
                                    title: "get()",
                                    type: "method",
                                    description: "访问指定URL",
                                    code: `# get() 方法详解
page = ChromiumPage()

# 基本用法
page.get('https://www.baidu.com')

# 带参数
page.get('https://httpbin.org/get',
         params={'key': 'value'})

# 设置超时
page.get('https://example.com', timeout=30)

# 会话模式下的get
session_page = SessionPage()
response = session_page.get('https://httpbin.org/get')
print(response.status_code)
print(response.headers)`
                                },
                                {
                                    title: "post()",
                                    type: "method",
                                    description: "发送POST请求",
                                    code: `# post() 方法详解
page = SessionPage()

# 发送表单数据
response = page.post('https://httpbin.org/post',
                    data={'username': 'test',
                          'password': '123456'})

# 发送JSON数据
response = page.post('https://httpbin.org/post',
                    json={'name': 'DrissionPage',
                          'type': 'automation'})

# 发送文件
with open('file.txt', 'rb') as f:
    response = page.post('https://httpbin.org/post',
                        files={'file': f})

print(response.json())`
                                },
                                {
                                    title: "refresh()",
                                    type: "method",
                                    description: "刷新当前页面",
                                    code: `# refresh() 方法详解
page = ChromiumPage()
page.get('https://www.baidu.com')

# 基本刷新
page.refresh()

# 强制刷新（忽略缓存）
page.refresh(ignore_cache=True)

# 等待刷新完成
page.refresh()
page.wait.load_start()`
                                },
                                {
                                    title: "back() / forward()",
                                    type: "method",
                                    description: "页面前进后退操作",
                                    code: `# 页面导航方法
page = ChromiumPage()

# 访问多个页面
page.get('https://www.baidu.com')
page.get('https://www.google.com')

# 后退
page.back()
print(page.url)  # 应该回到百度

# 前进
page.forward()
print(page.url)  # 应该回到谷歌

# 检查是否可以后退/前进
can_back = page.can_back
can_forward = page.can_forward`
                                }
                            ]
                        },
                        {
                            title: "元素查找方法",
                            type: "module",
                            description: "查找页面元素的各种方法",
                            children: [
                                {
                                    title: "ele()",
                                    type: "method",
                                    description: "查找单个元素",
                                    code: `# ele() 方法详解
page = ChromiumPage()
page.get('https://www.baidu.com')

# 通过ID查找
search_box = page.ele('#kw')

# 通过class查找
button = page.ele('.s_btn')

# 通过标签名查找
input_elem = page.ele('input')

# 通过属性查找
submit_btn = page.ele('@value=百度一下')

# 通过文本查找
link = page.ele('text:新闻')

# 通过CSS选择器查找
elem = page.ele('css:input[name="wd"]')

# 通过XPath查找
elem = page.ele('xpath://input[@id="kw"]')

# 链式查找
form = page.ele('form')
input_in_form = form.ele('input')`
                                },
                                {
                                    title: "eles()",
                                    type: "method",
                                    description: "查找多个元素",
                                    code: `# eles() 方法详解
page = SessionPage()
page.get('https://news.ycombinator.com')

# 查找所有链接
links = page.eles('a')
for link in links:
    print(link.text, link.link)

# 查找所有标题
titles = page.eles('.titleline>a')
for title in titles:
    print(title.text)

# 查找表格行
rows = page.eles('tr')
for i, row in enumerate(rows[:5]):
    print(f"Row {i}: {row.text}")

# 带索引的查找
first_link = page.eles('a')[0]
last_link = page.eles('a')[-1]

# 切片操作
first_five_links = page.eles('a')[:5]`
                                },
                                {
                                    title: "s_ele() / s_eles()",
                                    type: "method",
                                    description: "会话模式专用的元素查找方法",
                                    code: `# s_ele() 和 s_eles() 方法
page = SessionPage()
page.get('https://httpbin.org/html')

# s_ele() 查找单个元素
title = page.s_ele('h1')
print(title.text)

# s_eles() 查找多个元素
paragraphs = page.s_eles('p')
for p in paragraphs:
    print(p.text)

# 在WebPage中使用
web_page = WebPage()
web_page.change_mode('s')  # 切换到会话模式
elements = web_page.s_eles('.item')`
                                }
                            ]
                        },
                        {
                            title: "元素操作方法",
                            type: "module",
                            description: "对页面元素进行操作的方法",
                            children: [
                                {
                                    title: "click()",
                                    type: "method",
                                    description: "点击元素",
                                    code: `# click() 方法详解
page = ChromiumPage()
page.get('https://www.baidu.com')

# 基本点击
search_btn = page.ele('#su')
search_btn.click()

# 右键点击
search_btn.click.right()

# 中键点击
search_btn.click.middle()

# 双击
search_btn.click.double()

# 点击并等待
search_btn.click()
page.wait.load_start()

# 强制点击（即使元素被遮挡）
search_btn.click(force=True)

# 点击坐标偏移
search_btn.click(offset_x=10, offset_y=5)`
                                },
                                {
                                    title: "input()",
                                    type: "method",
                                    description: "向输入框输入文本",
                                    code: `# input() 方法详解
page = ChromiumPage()
page.get('https://www.baidu.com')

search_box = page.ele('#kw')

# 基本输入
search_box.input('DrissionPage')

# 清空后输入
search_box.input('新内容', clear=True)

# 逐字符输入（模拟真实打字）
search_box.input('慢速输入', by_char=True)

# 输入后按回车
search_box.input('搜索内容', by_enter=True)

# 输入特殊按键
search_box.input('文本')
search_box.input('\\n')  # 回车
search_box.input('\\t')  # Tab键

# 上传文件
file_input = page.ele('input[type="file"]')
file_input.input(r'C:\\path\\to\\file.txt')`
                                },
                                {
                                    title: "scroll()",
                                    type: "method",
                                    description: "滚动元素或页面",
                                    code: `# scroll() 方法详解
page = ChromiumPage()
page.get('https://www.baidu.com')

# 页面滚动
page.scroll.to_bottom()  # 滚动到底部
page.scroll.to_top()     # 滚动到顶部
page.scroll.to_half()    # 滚动到中间

# 按像素滚动
page.scroll(0, 500)      # 向下滚动500像素
page.scroll(0, -300)     # 向上滚动300像素

# 滚动到指定元素
element = page.ele('.footer')
page.scroll.to_see(element)

# 元素内部滚动
scrollable_div = page.ele('.scrollable')
scrollable_div.scroll.to_bottom()
scrollable_div.scroll(0, 100)`
                                }
                            ]
                        }
                    ]
                },
                {
                    title: "高级特性",
                    type: "category",
                    description: "DrissionPage的高级功能特性",
                    children: [
                        {
                            title: "网络监听",
                            type: "module",
                            description: "监听和拦截网络请求",
                            children: [
                                {
                                    title: "listen.start()",
                                    type: "method",
                                    description: "开始监听网络请求",
                                    code: `# 网络监听功能
page = ChromiumPage()

# 开始监听所有请求
page.listen.start()

# 监听特定URL模式
page.listen.start('api/data')
page.listen.start('*.json')

# 访问页面触发请求
page.get('https://httpbin.org')

# 等待特定请求
response = page.listen.wait()
print(response.url)
print(response.response.body)

# 获取所有监听到的请求
requests = page.listen.steps
for req in requests:
    print(f"URL: {req.url}")
    print(f"Method: {req.method}")
    print(f"Status: {req.response.status}")

# 停止监听
page.listen.stop()`
                                },
                                {
                                    title: "listen.wait()",
                                    type: "method",
                                    description: "等待特定的网络请求",
                                    code: `# 等待特定请求
page = ChromiumPage()
page.listen.start('api/user')

# 触发请求的操作
page.get('https://example.com')
page.ele('#load-data').click()

# 等待API请求完成
api_response = page.listen.wait(timeout=10)
if api_response:
    data = api_response.response.body
    print(f"API返回: {data}")
else:
    print("请求超时")

# 等待多个请求
page.listen.start(['api/user', 'api/posts'])
responses = page.listen.wait(count=2)
for resp in responses:
    print(f"收到响应: {resp.url}")`
                                }
                            ]
                        },
                        {
                            title: "文件下载",
                            type: "module",
                            description: "文件下载功能",
                            children: [
                                {
                                    title: "download()",
                                    type: "method",
                                    description: "下载文件",
                                    code: `# 文件下载功能
page = ChromiumPage()

# 基本下载
page.download('https://example.com/file.pdf')

# 指定保存路径
page.download('https://example.com/file.pdf',
             path='D:\\downloads\\')

# 指定文件名
page.download('https://example.com/file.pdf',
             path='D:\\downloads\\',
             rename='新文件名.pdf')

# 下载并等待完成
info = page.download('https://example.com/file.pdf')
print(f"下载完成: {info.path}")
print(f"文件大小: {info.size}")

# 会话模式下载
session_page = SessionPage()
session_page.download('https://example.com/data.zip',
                     path='./downloads/')`
                                },
                                {
                                    title: "wait.download_begin()",
                                    type: "method",
                                    description: "等待下载开始",
                                    code: `# 等待下载开始
page = ChromiumPage()
page.get('https://example.com/download-page')

# 设置下载监听
page.wait.download_begin()

# 点击下载链接
page.ele('#download-link').click()

# 等待下载开始
download_info = page.wait.download_begin(timeout=10)
if download_info:
    print(f"下载开始: {download_info.path}")
    print(f"文件名: {download_info.name}")
else:
    print("下载未开始")

# 等待下载完成
download_info.wait_done()
print("下载完成")`
                                }
                            ]
                        },
                        {
                            title: "JavaScript执行",
                            type: "module",
                            description: "在页面中执行JavaScript代码",
                            children: [
                                {
                                    title: "run_js()",
                                    type: "method",
                                    description: "执行JavaScript代码",
                                    code: `# JavaScript执行功能
page = ChromiumPage()
page.get('https://www.baidu.com')

# 执行简单JS代码
result = page.run_js('return document.title')
print(result)  # 页面标题

# 执行复杂JS代码
js_code = '''
function getData() {
    let data = [];
    let elements = document.querySelectorAll('.item');
    elements.forEach(el => {
        data.push({
            text: el.textContent,
            href: el.href
        });
    });
    return data;
}
return getData();
'''
data = page.run_js(js_code)

# 传递参数给JS
result = page.run_js('return arguments[0] + arguments[1]', 10, 20)
print(result)  # 30

# 操作页面元素
page.run_js('document.getElementById("kw").value = "DrissionPage"')
page.run_js('document.getElementById("su").click()')`
                                },
                                {
                                    title: "run_js_loaded()",
                                    type: "method",
                                    description: "页面加载完成后执行JavaScript",
                                    code: `# 页面加载完成后执行JS
page = ChromiumPage()

# 设置页面加载完成后要执行的JS
page.run_js_loaded('''
    console.log("页面加载完成");
    // 自动填充表单
    if (document.getElementById("username")) {
        document.getElementById("username").value = "testuser";
    }
''')

# 访问页面，JS会自动执行
page.get('https://example.com/login')

# 也可以在访问前设置多个JS
page.run_js_loaded('document.body.style.backgroundColor = "lightblue"')
page.get('https://example.com')`
                                }
                            ]
                        },
                        {
                            title: "动作链",
                            type: "module",
                            description: "复杂的鼠标和键盘操作序列",
                            children: [
                                {
                                    title: "actions.move_to()",
                                    type: "method",
                                    description: "移动鼠标到指定位置",
                                    code: `# 动作链 - 鼠标移动
page = ChromiumPage()
page.get('https://www.baidu.com')

# 移动到元素
search_box = page.ele('#kw')
page.actions.move_to(search_box)

# 移动到坐标
page.actions.move_to(100, 200)

# 移动到元素的偏移位置
page.actions.move_to(search_box, 10, 5)

# 执行动作
page.actions.perform()`
                                },
                                {
                                    title: "actions.click() / drag()",
                                    type: "method",
                                    description: "动作链中的点击和拖拽操作",
                                    code: `# 动作链 - 点击和拖拽
page = ChromiumPage()
page.get('https://example.com/drag-demo')

# 复杂的动作序列
source = page.ele('#source')
target = page.ele('#target')

# 拖拽操作
page.actions.move_to(source).click_and_hold().move_to(target).release().perform()

# 或者使用drag方法
page.actions.drag(source, target)

# 右键点击
page.actions.move_to(source).context_click().perform()

# 双击
page.actions.move_to(source).double_click().perform()

# 组合操作
page.actions.move_to(source).click().key_down('ctrl').click(target).key_up('ctrl').perform()`
                                },
                                {
                                    title: "actions.key_down() / key_up()",
                                    type: "method",
                                    description: "键盘按键操作",
                                    code: `# 动作链 - 键盘操作
page = ChromiumPage()
page.get('https://www.baidu.com')

search_box = page.ele('#kw')

# 按键操作
page.actions.click(search_box).key_down('shift').send_keys('hello').key_up('shift').perform()

# 组合键
page.actions.key_down('ctrl').send_keys('a').key_up('ctrl').perform()  # Ctrl+A
page.actions.key_down('ctrl').send_keys('c').key_up('ctrl').perform()  # Ctrl+C

# 特殊按键
page.actions.send_keys('\\ue007').perform()  # 回车键
page.actions.send_keys('\\ue009').perform()  # Tab键
page.actions.send_keys('\\ue00c').perform()  # 清除键

# 复杂的文本输入
page.actions.click(search_box).send_keys('DrissionPage').key_down('shift').send_keys(' 教程').key_up('shift').perform()`
                                }
                            ]
                        }
                    ]
                },
                {
                    title: "工具和辅助",
                    type: "category",
                    description: "辅助工具和实用功能",
                    children: [
                        {
                            title: "等待机制",
                            type: "module",
                            description: "各种等待条件和超时控制",
                            children: [
                                {
                                    title: "wait.load_start()",
                                    type: "method",
                                    description: "等待页面开始加载",
                                    code: `# 等待页面加载
page = ChromiumPage()

# 等待页面开始加载
page.get('https://www.baidu.com')
page.wait.load_start()

# 设置超时时间
page.wait.load_start(timeout=10)

# 在操作后等待加载
page.ele('#su').click()
page.wait.load_start()  # 等待搜索结果页面开始加载`
                                },
                                {
                                    title: "wait.doc_loaded()",
                                    type: "method",
                                    description: "等待文档加载完成",
                                    code: `# 等待文档加载完成
page = ChromiumPage()
page.get('https://www.baidu.com')

# 等待DOM加载完成
page.wait.doc_loaded()

# 等待页面完全加载（包括图片、CSS等）
page.wait.doc_loaded(load_mode='complete')

# 设置超时
page.wait.doc_loaded(timeout=30)`
                                },
                                {
                                    title: "wait.ele_loaded() / ele_displayed()",
                                    type: "method",
                                    description: "等待元素加载和显示",
                                    code: `# 等待元素状态
page = ChromiumPage()
page.get('https://example.com')

# 等待元素加载到DOM
page.wait.ele_loaded('#dynamic-content')

# 等待元素可见
page.wait.ele_displayed('#popup')

# 等待元素消失
page.wait.ele_hidden('#loading')

# 等待元素可点击
page.wait.ele_clickable('#submit-btn')

# 等待元素包含特定文本
page.wait.ele_text('#status', 'Complete')

# 组合等待条件
page.wait.ele_loaded('#form') and page.wait.ele_displayed('#form')`
                                }
                            ]
                        },
                        {
                            title: "错误处理",
                            type: "module",
                            description: "异常类型和错误处理策略",
                            children: [
                                {
                                    title: "ElementNotFoundError",
                                    type: "class",
                                    description: "元素未找到异常",
                                    code: `# 元素未找到异常处理
from DrissionPage.errors import ElementNotFoundError

page = ChromiumPage()
page.get('https://www.baidu.com')

try:
    # 查找不存在的元素
    element = page.ele('#non-existent')
    element.click()
except ElementNotFoundError as e:
    print(f"元素未找到: {e}")
    # 处理异常，比如使用备用方案
    backup_element = page.ele('.backup-selector')
    if backup_element:
        backup_element.click()

# 使用超时避免异常
element = page.ele('#maybe-exists', timeout=5)
if element:
    element.click()
else:
    print("元素在5秒内未出现")`
                                },
                                {
                                    title: "PageDisconnectedError",
                                    type: "class",
                                    description: "页面连接断开异常",
                                    code: `# 页面连接断开异常处理
from DrissionPage.errors import PageDisconnectedError

page = ChromiumPage()

try:
    page.get('https://www.baidu.com')
    # 模拟页面关闭或浏览器崩溃
    page.ele('#kw').input('test')
except PageDisconnectedError as e:
    print(f"页面连接断开: {e}")
    # 重新连接或创建新页面
    page = ChromiumPage()
    page.get('https://www.baidu.com')

# 检查页面连接状态
if page.is_alive:
    page.ele('#kw').input('test')
else:
    print("页面已断开连接")`
                                },
                                {
                                    title: "TimeoutError",
                                    type: "class",
                                    description: "超时异常",
                                    code: `# 超时异常处理
from DrissionPage.errors import TimeoutError

page = ChromiumPage()
page.get('https://slow-website.com')

try:
    # 设置较短的超时时间
    page.wait.ele_loaded('#slow-element', timeout=5)
except TimeoutError as e:
    print(f"等待超时: {e}")
    # 处理超时情况
    print("元素加载超时，继续执行其他操作")

# 在元素查找中使用超时
try:
    element = page.ele('#dynamic-content', timeout=10)
    element.click()
except TimeoutError:
    print("元素查找超时")
except ElementNotFoundError:
    print("元素不存在")`
                                }
                            ]
                        },
                        {
                            title: "配置管理",
                            type: "module",
                            description: "全局设置和配置管理",
                            children: [
                                {
                                    title: "Settings",
                                    type: "class",
                                    description: "全局设置管理",
                                    code: `# 全局设置管理
from DrissionPage.common import Settings

# 设置全局超时时间
Settings.timeout = 20

# 设置元素查找超时
Settings.element_timeout = 10

# 设置页面加载超时
Settings.page_load_timeout = 30

# 设置隐式等待
Settings.implicit_wait_time = 5

# 设置重试次数
Settings.retry_times = 3

# 设置重试间隔
Settings.retry_interval = 1

# 查看当前设置
print(f"当前超时设置: {Settings.timeout}")
print(f"元素超时设置: {Settings.element_timeout}")`
                                },
                                {
                                    title: "DriverOptions",
                                    type: "class",
                                    description: "驱动器选项配置",
                                    code: `# 驱动器选项配置
from DrissionPage.common import DriverOptions

# 创建驱动器选项
options = DriverOptions()

# 设置浏览器路径
options.binary_location = r'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe'

# 设置用户数据目录
options.user_data_dir = r'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data'

# 添加启动参数
options.add_argument('--no-sandbox')
options.add_argument('--disable-dev-shm-usage')
options.add_argument('--disable-gpu')

# 设置窗口大小
options.add_argument('--window-size=1920,1080')

# 设置代理
options.add_argument('--proxy-server=http://proxy.example.com:8080')

# 禁用图片加载
options.add_experimental_option('prefs', {
    'profile.managed_default_content_settings.images': 2
})

# 使用配置创建页面
page = ChromiumPage(driver_or_options=options)`
                                }
                            ]
                        }
                    ]
                },
                {
                    title: "最佳实践",
                    type: "category",
                    description: "使用DrissionPage的最佳实践和常见模式",
                    children: [
                        {
                            title: "使用模式",
                            type: "module",
                            description: "不同场景下的最佳使用模式",
                            children: [
                                {
                                    title: "数据采集模式",
                                    type: "feature",
                                    description: "高效的数据采集实践",
                                    code: `# 数据采集最佳实践
from DrissionPage import SessionPage, WebPage
import time

# 1. 纯数据采集使用SessionPage
def scrape_with_session():
    page = SessionPage()

    # 设置请求头
    page.set.headers({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })

    # 批量采集
    urls = ['https://example.com/page1', 'https://example.com/page2']
    data = []

    for url in urls:
        page.get(url)
        items = page.eles('.item')
        for item in items:
            data.append({
                'title': item.ele('.title').text,
                'link': item.ele('a').link,
                'date': item.ele('.date').text
            })
        time.sleep(1)  # 避免请求过快

    return data

# 2. 需要登录的采集使用WebPage
def scrape_with_login():
    page = WebPage()

    # 浏览器模式登录
    page.get('https://example.com/login')
    page.ele('#username').input('your_username')
    page.ele('#password').input('your_password')
    page.ele('#login').click()
    page.wait.load_start()

    # 切换到会话模式进行高效采集
    page.change_mode('s')

    # 采集需要登录的数据
    page.get('https://example.com/protected-data')
    data = page.eles('.protected-item')

    return [item.text for item in data]`
                                },
                                {
                                    title: "自动化测试模式",
                                    type: "feature",
                                    description: "Web自动化测试的最佳实践",
                                    code: `# 自动化测试最佳实践
from DrissionPage import ChromiumPage
from DrissionPage.errors import ElementNotFoundError, TimeoutError

class WebTestSuite:
    def __init__(self):
        self.page = ChromiumPage()
        self.base_url = 'https://example.com'

    def setup(self):
        """测试前置设置"""
        self.page.get(f'{self.base_url}/login')
        self.login('testuser', 'testpass')

    def teardown(self):
        """测试后置清理"""
        self.page.close()

    def login(self, username, password):
        """登录功能"""
        try:
            self.page.ele('#username').input(username)
            self.page.ele('#password').input(password)
            self.page.ele('#login-btn').click()

            # 等待登录成功
            self.page.wait.ele_loaded('#dashboard', timeout=10)
            return True
        except (ElementNotFoundError, TimeoutError):
            return False

    def test_form_submission(self):
        """测试表单提交"""
        self.page.get(f'{self.base_url}/form')

        # 填写表单
        self.page.ele('#name').input('Test User')
        self.page.ele('#email').input('<EMAIL>')
        self.page.ele('#message').input('This is a test message')

        # 提交表单
        self.page.ele('#submit').click()

        # 验证结果
        success_msg = self.page.ele('.success-message')
        assert success_msg.text == '提交成功'

    def test_file_upload(self):
        """测试文件上传"""
        self.page.get(f'{self.base_url}/upload')

        # 上传文件
        file_input = self.page.ele('input[type="file"]')
        file_input.input(r'C:\\test\\sample.txt')

        self.page.ele('#upload-btn').click()

        # 等待上传完成
        self.page.wait.ele_text('#status', '上传完成', timeout=30)`
                                }
                            ]
                        },
                        {
                            title: "性能优化",
                            type: "module",
                            description: "提升性能的技巧和方法",
                            children: [
                                {
                                    title: "资源优化",
                                    type: "feature",
                                    description: "减少资源加载提升速度",
                                    code: `# 性能优化技巧
from DrissionPage import ChromiumOptions, ChromiumPage

# 1. 禁用不必要的资源
co = ChromiumOptions()

# 禁用图片加载
co.no_imgs(True)

# 禁用CSS加载
co.add_argument('--disable-web-security')
co.add_experimental_option('prefs', {
    'profile.managed_default_content_settings.stylesheets': 2
})

# 禁用JavaScript（如果不需要）
co.add_experimental_option('prefs', {
    'profile.managed_default_content_settings.javascript': 2
})

# 设置页面加载策略
co.add_argument('--aggressive-cache-discard')

# 2. 使用无头模式
co.headless()

# 3. 设置合适的窗口大小
co.set_window_size(1024, 768)

page = ChromiumPage(co)

# 4. 设置加载模式
page.set.load_mode.none()  # 不等待页面完全加载

# 5. 批量操作优化
def batch_scraping():
    urls = ['url1', 'url2', 'url3']
    results = []

    for url in urls:
        page.get(url)
        # 只等待必要的元素
        page.wait.ele_loaded('.content')

        # 快速提取数据
        data = page.eles('.item')
        results.extend([item.text for item in data])

        # 清理页面缓存
        page.run_js('window.stop()')

    return results`
                                },
                                {
                                    title: "内存管理",
                                    type: "feature",
                                    description: "避免内存泄漏的方法",
                                    code: `# 内存管理最佳实践
from DrissionPage import ChromiumPage
import gc

class ManagedScraper:
    def __init__(self):
        self.page = None

    def __enter__(self):
        self.page = ChromiumPage()
        return self.page

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.page:
            self.page.quit()  # 关闭浏览器进程
        gc.collect()  # 强制垃圾回收

# 使用上下文管理器
def safe_scraping():
    with ManagedScraper() as page:
        page.get('https://example.com')
        data = page.eles('.item')
        return [item.text for item in data]

# 长时间运行的任务
def long_running_task():
    page = ChromiumPage()

    try:
        for i in range(1000):
            page.get(f'https://example.com/page/{i}')

            # 定期清理
            if i % 100 == 0:
                # 清理浏览器缓存
                page.run_js('window.location.reload(true)')
                # 关闭多余的标签
                tabs = page.get_tabs()
                if len(tabs) > 5:
                    for tab in tabs[5:]:
                        tab.close()

    finally:
        page.quit()`
                                }
                            ]
                        }
                    ]
                }
            ]
        };

        // 渲染思维导图
        function renderMindmap(data, container, level = 0) {
            const nodeDiv = document.createElement('div');
            nodeDiv.className = `node node-level-${level}`;
            nodeDiv.setAttribute('data-level', level);
            nodeDiv.setAttribute('data-title', data.title.toLowerCase());
            nodeDiv.setAttribute('data-description', data.description.toLowerCase());
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'node-content';
            
            if (data.children && data.children.length > 0) {
                const expandIcon = document.createElement('span');
                expandIcon.className = 'expand-icon';
                expandIcon.textContent = '▶';
                contentDiv.appendChild(expandIcon);
            }
            
            const iconSpan = document.createElement('span');
            iconSpan.className = 'node-icon';
            iconSpan.textContent = getNodeIcon(data.type);
            contentDiv.appendChild(iconSpan);
            
            const titleSpan = document.createElement('span');
            titleSpan.className = 'node-title';
            titleSpan.textContent = data.title;
            contentDiv.appendChild(titleSpan);
            
            const tagSpan = document.createElement('span');
            tagSpan.className = 'node-tag';
            tagSpan.textContent = data.type;
            contentDiv.appendChild(tagSpan);
            
            nodeDiv.appendChild(contentDiv);
            
            if (data.description) {
                const descDiv = document.createElement('div');
                descDiv.className = 'node-description';
                descDiv.textContent = data.description;
                nodeDiv.appendChild(descDiv);
            }
            
            if (data.code) {
                const codeDiv = document.createElement('div');
                codeDiv.className = 'code-example';
                codeDiv.innerHTML = `<pre><code>${data.code}</code></pre>`;
                nodeDiv.appendChild(codeDiv);
            }
            
            container.appendChild(nodeDiv);
            
            if (data.children && data.children.length > 0) {
                const childrenContainer = document.createElement('div');
                childrenContainer.className = 'children-container';
                childrenContainer.style.display = 'none';
                
                data.children.forEach(child => {
                    renderMindmap(child, childrenContainer, level + 1);
                });
                
                container.appendChild(childrenContainer);
                
                nodeDiv.addEventListener('click', function(e) {
                    e.stopPropagation();
                    toggleNode(this, childrenContainer);
                });
            } else if (data.code) {
                nodeDiv.addEventListener('click', function(e) {
                    e.stopPropagation();
                    toggleCodeExample(this);
                });
            }
        }

        function getNodeIcon(type) {
            const icons = {
                'root': '🌟',
                'category': '📁',
                'module': '📦',
                'class': '🏗️',
                'method': '⚙️',
                'property': '🔧',
                'event': '⚡',
                'syntax': '📝',
                'feature': '✨'
            };
            return icons[type] || '📄';
        }

        function toggleNode(nodeElement, childrenContainer) {
            const expandIcon = nodeElement.querySelector('.expand-icon');
            if (expandIcon) {
                const isExpanded = childrenContainer.style.display !== 'none';
                childrenContainer.style.display = isExpanded ? 'none' : 'block';
                expandIcon.classList.toggle('expanded', !isExpanded);
            }
        }

        function toggleCodeExample(nodeElement) {
            const codeExample = nodeElement.querySelector('.code-example');
            if (codeExample) {
                codeExample.classList.toggle('show');
            }
        }

        // 搜索功能
        function searchNodes(query) {
            const nodes = document.querySelectorAll('.node');
            const lowerQuery = query.toLowerCase();
            
            nodes.forEach(node => {
                const title = node.getAttribute('data-title') || '';
                const description = node.getAttribute('data-description') || '';
                const isMatch = title.includes(lowerQuery) || description.includes(lowerQuery);
                
                if (query === '') {
                    node.classList.remove('hidden', 'highlight');
                } else if (isMatch) {
                    node.classList.remove('hidden');
                    node.classList.add('highlight');
                } else {
                    node.classList.add('hidden');
                    node.classList.remove('highlight');
                }
            });
        }

        // 控制功能
        function expandAll() {
            document.querySelectorAll('.children-container').forEach(container => {
                container.style.display = 'block';
            });
            document.querySelectorAll('.expand-icon').forEach(icon => {
                icon.classList.add('expanded');
            });
        }

        function collapseAll() {
            document.querySelectorAll('.children-container').forEach(container => {
                container.style.display = 'none';
            });
            document.querySelectorAll('.expand-icon').forEach(icon => {
                icon.classList.remove('expanded');
            });
        }

        function showAllExamples() {
            document.querySelectorAll('.code-example').forEach(example => {
                example.classList.add('show');
            });
        }

        function hideAllExamples() {
            document.querySelectorAll('.code-example').forEach(example => {
                example.classList.remove('show');
            });
        }

        function showListView() {
            // 当前已经是列表视图
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.getElementById('mindmap-tree');
            renderMindmap(mindmapData, container);
        });
    </script>
</body>
</html>
