<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DrissionPage 交互式图形思维导图</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .title {
            font-size: 1.5em;
            font-weight: 700;
            color: #2c3e50;
        }

        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .control-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            background: #3498db;
            color: white;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .control-btn.secondary {
            background: #95a5a6;
        }

        .control-btn.secondary:hover {
            background: #7f8c8d;
        }

        .control-btn.danger {
            background: #e74c3c;
        }

        .control-btn.danger:hover {
            background: #c0392b;
        }

        .search-input {
            padding: 8px 15px;
            border: 2px solid #ddd;
            border-radius: 20px;
            font-size: 0.9em;
            outline: none;
            width: 200px;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: #3498db;
        }

        #graph-container {
            width: 100vw;
            height: 100vh;
            padding-top: 80px;
        }

        .node {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .node rect {
            stroke-width: 2;
            rx: 8;
            ry: 8;
        }

        .node text {
            font-family: 'Segoe UI', sans-serif;
            font-weight: 600;
            text-anchor: middle;
            dominant-baseline: middle;
            pointer-events: none;
        }

        .node.root rect {
            fill: #e74c3c;
            stroke: #c0392b;
        }

        .node.root text {
            fill: white;
            font-size: 16px;
        }

        .node.category rect {
            fill: #3498db;
            stroke: #2980b9;
        }

        .node.category text {
            fill: white;
            font-size: 14px;
        }

        .node.module rect {
            fill: #2ecc71;
            stroke: #27ae60;
        }

        .node.module text {
            fill: white;
            font-size: 12px;
        }

        .node.class rect {
            fill: #f39c12;
            stroke: #e67e22;
        }

        .node.class text {
            fill: white;
            font-size: 11px;
        }

        .node.method rect {
            fill: #9b59b6;
            stroke: #8e44ad;
        }

        .node.method text {
            fill: white;
            font-size: 10px;
        }

        .node.feature rect {
            fill: #1abc9c;
            stroke: #16a085;
        }

        .node.feature text {
            fill: white;
            font-size: 11px;
        }

        .node:hover rect {
            stroke-width: 3;
            filter: brightness(1.1);
        }

        .link {
            fill: none;
            stroke: #7f8c8d;
            stroke-width: 2;
            stroke-opacity: 0.6;
        }

        .link.highlighted {
            stroke: #e74c3c;
            stroke-width: 3;
            stroke-opacity: 1;
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            max-width: 300px;
            pointer-events: none;
            z-index: 1001;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .tooltip.show {
            opacity: 1;
        }

        .zoom-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .zoom-btn {
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            color: #2c3e50;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .zoom-btn:hover {
            background: white;
            transform: scale(1.1);
        }

        .legend {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            z-index: 1000;
        }

        .legend-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
            margin-right: 8px;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 10px;
            }
            
            .controls {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .search-input {
                width: 150px;
            }
            
            #graph-container {
                padding-top: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="title">🌐 DrissionPage 交互式图形思维导图</div>
            <div class="controls">
                <input type="text" class="search-input" placeholder="🔍 搜索节点..." id="search-input">
                <button class="control-btn" onclick="expandAll()">➕ 展开全部</button>
                <button class="control-btn" onclick="collapseAll()">➖ 收起全部</button>
                <button class="control-btn" onclick="resetView()">🎯 重置视图</button>
                <button class="control-btn secondary" onclick="exportMermaid()">📊 导出Mermaid</button>
                <a href="drissionpage_complete_mindmap.html" class="control-btn danger">📋 返回列表</a>
            </div>
        </div>
    </div>

    <div id="graph-container"></div>

    <div class="zoom-controls">
        <button class="zoom-btn" onclick="zoomIn()">+</button>
        <button class="zoom-btn" onclick="zoomOut()">-</button>
        <button class="zoom-btn" onclick="fitToScreen()" style="font-size: 14px;">⌂</button>
    </div>

    <div class="legend">
        <div class="legend-title">节点类型</div>
        <div class="legend-item">
            <div class="legend-color" style="background: #e74c3c;"></div>
            <span>根节点</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #3498db;"></div>
            <span>分类节点</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #2ecc71;"></div>
            <span>模块节点</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #f39c12;"></div>
            <span>类节点</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #9b59b6;"></div>
            <span>方法节点</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #1abc9c;"></div>
            <span>特性节点</span>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        // 思维导图数据（简化版，用于图形展示）
        const graphData = {
            name: "DrissionPage",
            type: "root",
            description: "Python 强大优雅的网页自动化工具",
            children: [
                {
                    name: "核心架构",
                    type: "category",
                    description: "DrissionPage的基础架构和核心组件",
                    children: [
                        {
                            name: "基础组件",
                            type: "module",
                            children: [
                                { name: "BasePage", type: "class", description: "所有页面对象的基类" },
                                { name: "Driver", type: "class", description: "Chrome DevTools Protocol 驱动器" },
                                { name: "Chromium", type: "class", description: "浏览器管理器" }
                            ]
                        },
                        {
                            name: "页面对象",
                            type: "module",
                            children: [
                                { name: "ChromiumPage", type: "class", description: "浏览器模式页面对象" },
                                { name: "SessionPage", type: "class", description: "会话模式页面对象" },
                                { name: "WebPage", type: "class", description: "混合模式页面对象" }
                            ]
                        },
                        {
                            name: "配置系统",
                            type: "module",
                            children: [
                                { name: "ChromiumOptions", type: "class", description: "浏览器配置选项" },
                                { name: "SessionOptions", type: "class", description: "会话配置选项" }
                            ]
                        }
                    ]
                },
                {
                    name: "主要功能模块",
                    type: "category",
                    description: "DrissionPage的核心功能模块",
                    children: [
                        {
                            name: "浏览器控制模式",
                            type: "module",
                            children: [
                                { name: "页面导航", type: "feature", description: "页面访问和导航控制" },
                                { name: "标签管理", type: "feature", description: "浏览器标签页的创建和管理" },
                                { name: "窗口操作", type: "feature", description: "浏览器窗口的控制" }
                            ]
                        },
                        {
                            name: "会话请求模式",
                            type: "module",
                            children: [
                                { name: "HTTP请求", type: "feature", description: "各种HTTP请求方法" },
                                { name: "数据提取", type: "feature", description: "从HTML响应中提取数据" }
                            ]
                        },
                        {
                            name: "混合模式",
                            type: "module",
                            children: [
                                { name: "模式切换", type: "feature", description: "在不同模式间切换" },
                                { name: "状态保持", type: "feature", description: "在模式切换时保持登录状态" }
                            ]
                        }
                    ]
                },
                {
                    name: "API参考",
                    type: "category",
                    description: "完整的API方法参考",
                    children: [
                        {
                            name: "页面操作方法",
                            type: "module",
                            children: [
                                { name: "get()", type: "method", description: "访问指定URL" },
                                { name: "post()", type: "method", description: "发送POST请求" },
                                { name: "refresh()", type: "method", description: "刷新当前页面" },
                                { name: "back()", type: "method", description: "页面后退" },
                                { name: "forward()", type: "method", description: "页面前进" }
                            ]
                        },
                        {
                            name: "元素查找方法",
                            type: "module",
                            children: [
                                { name: "ele()", type: "method", description: "查找单个元素" },
                                { name: "eles()", type: "method", description: "查找多个元素" },
                                { name: "s_ele()", type: "method", description: "会话模式查找单个元素" },
                                { name: "s_eles()", type: "method", description: "会话模式查找多个元素" }
                            ]
                        },
                        {
                            name: "元素操作方法",
                            type: "module",
                            children: [
                                { name: "click()", type: "method", description: "点击元素" },
                                { name: "input()", type: "method", description: "向输入框输入文本" },
                                { name: "scroll()", type: "method", description: "滚动元素或页面" },
                                { name: "select()", type: "method", description: "选择下拉框选项" },
                                { name: "drag()", type: "method", description: "拖拽元素" }
                            ]
                        }
                    ]
                },
                {
                    name: "高级特性",
                    type: "category",
                    description: "DrissionPage的高级功能特性",
                    children: [
                        {
                            name: "网络监听",
                            type: "module",
                            children: [
                                { name: "listen.start()", type: "method", description: "开始监听网络请求" },
                                { name: "listen.wait()", type: "method", description: "等待特定的网络请求" },
                                { name: "listen.stop()", type: "method", description: "停止网络监听" }
                            ]
                        },
                        {
                            name: "文件下载",
                            type: "module",
                            children: [
                                { name: "download()", type: "method", description: "下载文件" },
                                { name: "wait.download_begin()", type: "method", description: "等待下载开始" }
                            ]
                        },
                        {
                            name: "JavaScript执行",
                            type: "module",
                            children: [
                                { name: "run_js()", type: "method", description: "执行JavaScript代码" },
                                { name: "run_js_loaded()", type: "method", description: "页面加载完成后执行JS" }
                            ]
                        },
                        {
                            name: "动作链",
                            type: "module",
                            children: [
                                { name: "actions.move_to()", type: "method", description: "移动鼠标到元素" },
                                { name: "actions.click()", type: "method", description: "动作链点击" },
                                { name: "actions.drag()", type: "method", description: "动作链拖拽" },
                                { name: "actions.key_down()", type: "method", description: "按下按键" },
                                { name: "actions.key_up()", type: "method", description: "释放按键" }
                            ]
                        }
                    ]
                },
                {
                    name: "工具和辅助",
                    type: "category",
                    description: "辅助工具和实用功能",
                    children: [
                        {
                            name: "等待机制",
                            type: "module",
                            children: [
                                { name: "wait.load_start()", type: "method", description: "等待页面开始加载" },
                                { name: "wait.doc_loaded()", type: "method", description: "等待文档加载完成" },
                                { name: "wait.ele_loaded()", type: "method", description: "等待元素加载" },
                                { name: "wait.ele_displayed()", type: "method", description: "等待元素显示" }
                            ]
                        },
                        {
                            name: "错误处理",
                            type: "module",
                            children: [
                                { name: "ElementNotFoundError", type: "class", description: "元素未找到异常" },
                                { name: "PageDisconnectedError", type: "class", description: "页面连接断开异常" },
                                { name: "TimeoutError", type: "class", description: "超时异常" }
                            ]
                        },
                        {
                            name: "配置管理",
                            type: "module",
                            children: [
                                { name: "Settings", type: "class", description: "全局设置管理" },
                                { name: "DriverOptions", type: "class", description: "驱动器选项配置" }
                            ]
                        }
                    ]
                }
            ]
        };

        // 全局变量
        let svg, g, root, tree, nodes, links;
        let zoom, tooltip;
        let width = window.innerWidth;
        let height = window.innerHeight - 80;

        // 初始化图形
        function initGraph() {
            // 创建SVG
            svg = d3.select("#graph-container")
                .append("svg")
                .attr("width", width)
                .attr("height", height);

            // 创建缩放行为
            zoom = d3.zoom()
                .scaleExtent([0.1, 3])
                .on("zoom", (event) => {
                    g.attr("transform", event.transform);
                });

            svg.call(zoom);

            // 创建主容器
            g = svg.append("g");

            // 创建提示框
            tooltip = d3.select("#tooltip");

            // 创建树布局
            tree = d3.tree()
                .size([height - 100, width - 200])
                .separation((a, b) => (a.parent === b.parent ? 1 : 2) / a.depth);

            // 处理数据
            root = d3.hierarchy(graphData);
            root.x0 = height / 2;
            root.y0 = 0;

            // 初始化节点状态
            root.children.forEach(collapse);

            // 绘制图形
            update(root);

            // 居中显示
            centerNode(root);
        }

        // 更新图形
        function update(source) {
            const treeData = tree(root);
            nodes = treeData.descendants();
            links = treeData.descendants().slice(1);

            // 更新节点位置
            nodes.forEach(d => { d.y = d.depth * 200; });

            // 更新节点
            const node = g.selectAll('g.node')
                .data(nodes, d => d.id || (d.id = ++i));

            // 进入新节点
            const nodeEnter = node.enter().append('g')
                .attr('class', d => `node ${d.data.type}`)
                .attr("transform", d => `translate(${source.y0},${source.x0})`)
                .on('click', click)
                .on('mouseover', showTooltip)
                .on('mouseout', hideTooltip);

            // 添加矩形
            nodeEnter.append('rect')
                .attr('width', 1e-6)
                .attr('height', 1e-6)
                .attr('x', -0.5)
                .attr('y', -10);

            // 添加文本
            nodeEnter.append('text')
                .attr("dy", ".35em")
                .text(d => d.data.name)
                .style("fill-opacity", 1e-6);

            // 更新现有节点
            const nodeUpdate = nodeEnter.merge(node);

            nodeUpdate.transition()
                .duration(750)
                .attr("transform", d => `translate(${d.y},${d.x})`);

            nodeUpdate.select('rect')
                .transition()
                .duration(750)
                .attr('width', d => getNodeWidth(d))
                .attr('height', 30)
                .attr('x', d => -getNodeWidth(d) / 2)
                .attr('y', -15);

            nodeUpdate.select('text')
                .transition()
                .duration(750)
                .style("fill-opacity", 1);

            // 退出节点
            const nodeExit = node.exit().transition()
                .duration(750)
                .attr("transform", d => `translate(${source.y},${source.x})`)
                .remove();

            nodeExit.select('rect')
                .attr('width', 1e-6)
                .attr('height', 1e-6);

            nodeExit.select('text')
                .style('fill-opacity', 1e-6);

            // 更新连接线
            const link = g.selectAll('path.link')
                .data(links, d => d.id);

            const linkEnter = link.enter().insert('path', "g")
                .attr("class", "link")
                .attr('d', d => {
                    const o = {x: source.x0, y: source.y0};
                    return diagonal(o, o);
                });

            const linkUpdate = linkEnter.merge(link);

            linkUpdate.transition()
                .duration(750)
                .attr('d', d => diagonal(d, d.parent));

            const linkExit = link.exit().transition()
                .duration(750)
                .attr('d', d => {
                    const o = {x: source.x, y: source.y};
                    return diagonal(o, o);
                })
                .remove();

            // 存储旧位置
            nodes.forEach(d => {
                d.x0 = d.x;
                d.y0 = d.y;
            });
        }

        // 节点点击事件
        function click(event, d) {
            if (d.children) {
                d._children = d.children;
                d.children = null;
            } else {
                d.children = d._children;
                d._children = null;
            }
            update(d);
        }

        // 折叠节点
        function collapse(d) {
            if (d.children) {
                d._children = d.children;
                d._children.forEach(collapse);
                d.children = null;
            }
        }

        // 展开节点
        function expand(d) {
            if (d._children) {
                d.children = d._children;
                d.children.forEach(expand);
                d._children = null;
            }
        }

        // 获取节点宽度
        function getNodeWidth(d) {
            const textLength = d.data.name.length;
            const baseWidth = 80;
            const charWidth = 8;
            return Math.max(baseWidth, textLength * charWidth + 20);
        }

        // 对角线连接
        function diagonal(s, d) {
            const path = `M ${s.y} ${s.x}
                         C ${(s.y + d.y) / 2} ${s.x},
                           ${(s.y + d.y) / 2} ${d.x},
                           ${d.y} ${d.x}`;
            return path;
        }

        // 显示提示框
        function showTooltip(event, d) {
            tooltip.html(`
                <strong>${d.data.name}</strong><br>
                类型: ${d.data.type}<br>
                ${d.data.description ? `描述: ${d.data.description}` : ''}
            `)
            .style("left", (event.pageX + 10) + "px")
            .style("top", (event.pageY - 10) + "px")
            .classed("show", true);
        }

        // 隐藏提示框
        function hideTooltip() {
            tooltip.classed("show", false);
        }

        // 居中节点
        function centerNode(source) {
            const scale = 0.8;
            const x = -source.y0 * scale + width / 2;
            const y = -source.x0 * scale + height / 2;
            
            svg.transition()
                .duration(750)
                .call(zoom.transform, d3.zoomIdentity.translate(x, y).scale(scale));
        }

        // 控制函数
        function expandAll() {
            root.children.forEach(expand);
            update(root);
        }

        function collapseAll() {
            root.children.forEach(collapse);
            update(root);
        }

        function resetView() {
            centerNode(root);
        }

        function zoomIn() {
            svg.transition().call(zoom.scaleBy, 1.5);
        }

        function zoomOut() {
            svg.transition().call(zoom.scaleBy, 1 / 1.5);
        }

        function fitToScreen() {
            const bounds = g.node().getBBox();
            const fullWidth = width;
            const fullHeight = height;
            const widthScale = fullWidth / bounds.width;
            const heightScale = fullHeight / bounds.height;
            const scale = Math.min(widthScale, heightScale) * 0.9;
            const translate = [fullWidth / 2 - scale * (bounds.x + bounds.width / 2), 
                              fullHeight / 2 - scale * (bounds.y + bounds.height / 2)];
            
            svg.transition()
                .duration(750)
                .call(zoom.transform, d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale));
        }

        function exportMermaid() {
            let mermaidCode = "graph TD\n";
            
            function addNode(node, parentId = null) {
                const nodeId = node.data.name.replace(/\s+/g, '_').replace(/[^\w]/g, '');
                const nodeLabel = node.data.name;
                
                if (parentId) {
                    mermaidCode += `    ${parentId} --> ${nodeId}[${nodeLabel}]\n`;
                } else {
                    mermaidCode += `    ${nodeId}[${nodeLabel}]\n`;
                }
                
                if (node.children) {
                    node.children.forEach(child => addNode(child, nodeId));
                }
            }
            
            addNode(root);
            
            const blob = new Blob([mermaidCode], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'drissionpage_mindmap.mmd';
            a.click();
            URL.revokeObjectURL(url);
        }

        // 搜索功能
        document.getElementById('search-input').addEventListener('input', function(e) {
            const query = e.target.value.toLowerCase();
            
            g.selectAll('.node').classed('highlighted', false);
            g.selectAll('.link').classed('highlighted', false);
            
            if (query) {
                const matchingNodes = nodes.filter(d => 
                    d.data.name.toLowerCase().includes(query) ||
                    (d.data.description && d.data.description.toLowerCase().includes(query))
                );
                
                matchingNodes.forEach(node => {
                    g.selectAll('.node').filter(d => d === node).classed('highlighted', true);
                    
                    // 高亮路径
                    let current = node;
                    while (current.parent) {
                        g.selectAll('.link').filter(d => d === current).classed('highlighted', true);
                        current = current.parent;
                    }
                });
            }
        });

        // 窗口大小调整
        window.addEventListener('resize', function() {
            width = window.innerWidth;
            height = window.innerHeight - 80;
            
            svg.attr("width", width).attr("height", height);
            tree.size([height - 100, width - 200]);
            
            update(root);
        });

        // 初始化
        let i = 0;
        document.addEventListener('DOMContentLoaded', initGraph);
    </script>
</body>
</html>
